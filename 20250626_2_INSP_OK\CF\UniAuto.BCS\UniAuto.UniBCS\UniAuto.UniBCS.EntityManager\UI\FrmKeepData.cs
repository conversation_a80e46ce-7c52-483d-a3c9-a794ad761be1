﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Windows.Forms;
using Spring.Context;
using Spring.Objects.Factory;

using UniAuto.UniBCS.Core;
using UniAuto.UniBCS.Entity;
using UniAuto.UniBCS.Log;

namespace UniAuto.UniBCS.EntityManager.UI {

    public partial class FrmKeepData : Form //, IApplicationContextAware
    {
        private IApplicationContext _applicationContext;
        //private ObjectManager _managerObject = null;
        private IDictionary<string, DataTable> dataSet = new Dictionary<string, DataTable>();
        public IApplicationContext ApplicationContext {
            get { return _applicationContext; }
            set { _applicationContext = value; }
        }

        public FrmKeepData() {
            InitializeComponent();
        }

        public void Init() {
        }

        private void FrmKeepData_Load(object sender, EventArgs e) {
            try {

            } catch (Exception ex) {
                MessageBox.Show(ex.Message);
                Close();
            }
        }

        private void _dgvDataGird_MouseClick(object sender, MouseEventArgs e) {
            //if (e.Button == MouseButtons.Right && _treTreeView.SelectedNode != null) {

            //    if (_treTreeView.SelectedNode.Level > 0 && _treTreeView.SelectedNode.Tag.ToString() == "JobManager" && _dgvDataGrid.SelectedRows.Count > 0) {
            //        contextMenuStrip1.Show(_dgvDataGrid, e.X, e.Y);
            //    } else if (_treTreeView.SelectedNode.Level > 0 && _treTreeView.SelectedNode.Text == "CassetteManager" && _dgvDataGrid.SelectedRows.Count > 0) {
            //        contextMenuStrip2.Show(_dgvDataGrid, e.X, e.Y);
            //    } else if (_treTreeView.SelectedNode.Level > 0 && _treTreeView.SelectedNode.Text == "RobotJobManager" && _dgvDataGrid.SelectedRows.Count > 0) {
            //        contextMenuStrip2.Show(_dgvDataGrid, e.X, e.Y);
            //    }

            //}
        }

        private void deleteToolStripMenuItem_Click(object sender, EventArgs e) {
            if (_dgvDataGrid.SelectedRows.Count > 0) {
                if (MessageBox.Show("Do you wan't Delete Jobs", "Delete Job", MessageBoxButtons.OKCancel, MessageBoxIcon.Warning) != DialogResult.OK) {
                    return;
                }

                List<Job> jobs = new List<Job>();
                foreach (DataGridViewRow row in _dgvDataGrid.SelectedRows) {
                    string cstSeqNo = row.Cells["CassetteSequenceNo"].Value.ToString();
                    string jobSeqNo = row.Cells["JobSequenceNo"].Value.ToString();

                    Job job = null;// ObjectManager.JobManager.GetJob(cstSeqNo, jobSeqNo);
                    if (job != null) {
                        jobs.Add(job);
                    }

                }

                if (jobs.Count > 0) {
                    ObjectManager.JobManager.DeleteJobs(jobs, true); //v1.0.0.21-2

                    //_treTreeView_AfterSelect(null, null);
                }
            }
        }

        private void _dgvDataGird_MouseDoubleClick(object sender, MouseEventArgs e) {
            try {
                //ObjectMonitor monitor = new ObjectMonitor();
                //object obj = null;
                //string objectName = "";
                //if (_dgvDataGrid.SelectedRows.Count > 0) {
                //    if (_treTreeView.SelectedNode.Text == "EquipmentManager") {
                //        string nodeID = _dgvDataGrid.SelectedRows[0].Cells["NodeID"].Value.ToString();

                //        Equipment eq = ObjectManager.EquipmentManager.GetEQPByID(nodeID);
                //        if (eq != null) {
                //            obj = eq;
                //            objectName = nodeID;
                //        }
                //    } else if (_treTreeView.SelectedNode.Text == "JobManager") {
                //        string cstSeqNo = _dgvDataGrid.SelectedRows[0].Cells["CassetteSequenceNo"].Value.ToString();
                //        string jobSeqNo = _dgvDataGrid.SelectedRows[0].Cells["JobSequenceNo"].Value.ToString();

                //        Job job = null; // ObjectManager.JobManager.GetJob(cstSeqNo, jobSeqNo);
                //        if (job != null) {
                //            obj = job;
                //            objectName = cstSeqNo + "_" + jobSeqNo;
                //        }

                //    } else if (_treTreeView.SelectedNode.Text == "LineManager") {
                //        string lineName = _dgvDataGrid.SelectedRows[0].Cells["LineID"].Value.ToString();
                //        Line line = ObjectManager.LineManager.GetLine(lineName);
                //        if (line != null) {
                //            obj = line;
                //            objectName = lineName;
                //        }
                //    } else if (_treTreeView.SelectedNode.Text == "PortManager") {
                //        string portId = _dgvDataGrid.SelectedRows[0].Cells["PortID"].Value.ToString();
                //        Port port = ObjectManager.PortManager.GetPort(portId);
                //        if (port != null) {
                //            obj = port;
                //            objectName = portId;
                //        }
                //    } else if (_treTreeView.SelectedNode.Text == "CassetteManager") {
                //        string cstId = _dgvDataGrid.SelectedRows[0].Cells["_CassetteID"].Value.ToString();
                //        Cassette cst = ObjectManager.CassetteManager.GetCassette(cstId);
                //        if (cst != null) {
                //            obj = cst;
                //            objectName = cstId;
                //        }
                //    } else if (_treTreeView.SelectedNode.Text == "SubEquipmentManager") {
                //        string unitId = _dgvDataGrid.SelectedRows[0].Cells["UnitID"].Value.ToString();
                //        SubEquipment unit = ObjectManager.SubEquipmentManager.GetSubEquipment(unitId);
                //        if (unit != null) {
                //            obj = unit;
                //            objectName = unitId;
                //        }
                //    }

                //    if (obj != null) {
                //        monitor.ObjectName = objectName;
                //        monitor.SelectObject = obj;
                //        monitor.ShowDialog();
                //    }

                //}
            } catch (System.Exception ex) {
                NLogManager.Logger.LogErrorWrite("Service", this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", ex);
            }


        }

        private void removeToolStripMenuItem_Click(object sender, EventArgs e) {
            FrmRemoveRecoveryJob removeRecoveryJob = new FrmRemoveRecoveryJob(REMOVERECOVERY.REMOVE);
            string equipmentNo = "";
            string cstSeq = "";
            string slotNo = "";
            if (removeRecoveryJob.ShowDialog() == DialogResult.OK) {
                equipmentNo = removeRecoveryJob.EquipmentNo;
                cstSeq = removeRecoveryJob.CstSeq;
                slotNo = removeRecoveryJob.SlotNo;
                ObjectManager.JobManager.RemoveJobByUI(equipmentNo, cstSeq, slotNo);
                //string msg=ObjectManager.JobManager.RemoveJobByUI(equipmentNo, cstSeq, slotNo);
            }


        }

        private void recoveryToolStripMenuItem_Click(object sender, EventArgs e) {
            FrmRemoveRecoveryJob removeRecoveryJob = new FrmRemoveRecoveryJob(REMOVERECOVERY.RECOVERY);
            string equipmentNo = "";
            string cstSeq = "";
            string slotNo = "";
            if (removeRecoveryJob.ShowDialog() == DialogResult.OK) {
                equipmentNo = removeRecoveryJob.EquipmentNo;
                cstSeq = removeRecoveryJob.CstSeq;
                slotNo = removeRecoveryJob.SlotNo;
                ObjectManager.JobManager.RecoveryJobByUI(equipmentNo, cstSeq, slotNo);
                //string msg=ObjectManager.JobManager.RecoveryJobByUI(equipmentNo, cstSeq, slotNo);
            }
        }

        private void refreshToolStripMenuItem_Click(object sender, EventArgs e) {
            //_treTreeView_AfterSelect(null, null);
        }

        private void toolStripMenuItem1_Click(object sender, EventArgs e) {
            try {
                //if (_dgvDataGrid.SelectedRows.Count == 1) {
                //    if (_treTreeView.SelectedNode.Text == "JobManager") {
                //        string cstSeqNo = _dgvDataGrid.SelectedRows[0].Cells["CassetteSequenceNo"].Value.ToString();
                //        string jobSeqNo = _dgvDataGrid.SelectedRows[0].Cells["JobSequenceNo"].Value.ToString();

                //        Job job = null; // ObjectManager.JobManager.GetJob(cstSeqNo, jobSeqNo);
                //        if (job != null) {
                //            FrmObjectEdit edit = new FrmObjectEdit();
                //            edit.ObjectName = job._JobKey;
                //            edit.SelectObject = job;
                //            edit.Show();
                //        }

                //    }
                //}
            } catch (System.Exception ex) {
                NLogManager.Logger.LogErrorWrite("Service", this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", ex);

            }

        }

        private void editToolStripMenuItem_Click(object sender, EventArgs e) {
            try {
                //if (_dgvDataGrid.SelectedRows.Count == 1) {
                //    if (_treTreeView.SelectedNode.Text == "CassetteManager") {
                //        string cassetteID = _dgvDataGrid.SelectedRows[0].Cells["CassetteID"].Value.ToString();


                //        Cassette cassette = ObjectManager.CassetteManager.GetCassette(cassetteID);
                //        if (cassette != null) {
                //            FrmObjectEdit edit = new FrmObjectEdit();
                //            edit.ObjectName = cassette._CassetteID;
                //            edit.SelectObject = cassette;
                //            edit.Show();
                //        }

                //    }
                //    //else if (_treTreeView.SelectedNode.Text == "RobotJobManager")
                //    //{
                //    //    string cstSeqNo = _dgvDataGird.SelectedRows[0].Cells["CstSeqNo"].Value.ToString();
                //    //    string jobSeqNo = _dgvDataGird.SelectedRows[0].Cells["JobSeqNo"].Value.ToString();

                //    //    RobotJob job = ObjectManager.RobotJobManager.GetRobotJob(cstSeqNo, jobSeqNo);
                //    //    if (job != null)
                //    //    {
                //    //        FrmObjectEdit edit = new FrmObjectEdit();
                //    //        edit.ObjectName = job.JobKey;
                //    //        edit.SelectObject = job;
                //    //        edit.Show();
                //    //    }

                //    //}
                //}
            } catch (System.Exception ex) {
                NLogManager.Logger.LogErrorWrite("Service", this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", ex);

            }

        }

        private void _dgvDataGird_CellContentDoubleClick(object sender, DataGridViewCellEventArgs e) {
            //MessageBox.Show(string.Format("{0:D3}+{1:D3}", e.RowIndex, e.ColumnIndex));
            //switch (_treTreeView.SelectedNode.Text.ToLower()) {
            //    case "portmanager":



            //        break;
            //    case "cassettemanager":
            //        #region CassetteManager
            //        string _cassetteID = _dgvDataGrid["_CassetteID", e.RowIndex].Value.ToString();
            //        Cassette _cst = ObjectManager.CassetteManager.GetCassette(_cassetteID) as Cassette;
            //        if (_cst != null) {
            //            Port _port = ObjectManager.PortManager.GetPort(_cst._PortID);

            //            ObjectPropertyGrid _p = new ObjectPropertyGrid();
            //            _p.Text = string.Format("Cassette ID=[{0}], On Port=[{1}], Status=[{2}/{3}]", _cst._CassetteID, _cst._PortID, (_port == null ? "----" : _port.File._PortStatus.ToString()), _cst._CSTStatus.ToString());
            //            switch (_dgvDataGrid[e.ColumnIndex, e.RowIndex].OwningColumn.Name.ToLower()) {
            //                case "_bpiqcrrd":
            //                    _p.Text += " -[BPIQCRRD]";
            //                    _p.SelectedObject = _cst._BPIQCRRD;
            //                    break;
            //                case "_bpvryope":
            //                    _p.Text += " -[BPVRYOPE]";
            //                    _p.SelectedObject = _cst._BPVRYOPE;
            //                    break;
            //                case "_cstmapinfo":
            //                    _p.Text += " -[CSTMapInfo]";
            //                    _p.SelectedObject = _cst._CSTMapInfo;
            //                    break;
            //                case "_cstslot":
            //                    _p.Text += " -[CSTSlot]";
            //                    _p.SelectedObject = _cst._CSTSlot;
            //                    break;
            //                default:
            //                    _p.SelectedObject = _cst;
            //                    break;
            //            }
            //            _p.Show();
            //        }
            //        #endregion
            //        break;
            //    case "jobmanager":
            //        #region JobManager
            //        string[] _jobKey = _dgvDataGrid["_JobKey", e.RowIndex].Value.ToString().Split('_');

            //        Job _job = ObjectManager.JobManager.GetJob(_jobKey[0].ToString(), _jobKey[1].ToString(), _jobKey[2].ToString()) as Job;
            //        if (_job != null) {
            //            ObjectPropertyGrid _p = new ObjectPropertyGrid();
            //            _p.Text = string.Format("Job[{0}({1})], On Cassette=[{2}], On Port=[{3}]", _job._GlassNumberHex, _job._JobKey, _job._FromCstID, _job._FromPortID);
            //            switch (_dgvDataGrid[e.ColumnIndex, e.RowIndex].OwningColumn.Name.ToLower()) {
            //                case "_jobdata":
            //                    _p.Text += " -[JobData]";
            //                    _p.SelectedObject = _job._JobData;
            //                    break;
            //                case "_jobinfo":
            //                    _p.Text += " -[JobInfo]";
            //                    _p.SelectedObject = _job._JobInfo;
            //                    break;
            //                case "_glassid":
            //                    _p.Text += " -[GlassID]";
            //                    _p.SelectedObject = _job._GlassID;
            //                    break;
            //                case "_productinfo":
            //                    _p.Text += " -[ProductInfo]";
            //                    _p.SelectedObject = _job._ProductInfo;
            //                    break;
            //                case "_panelinfo":
            //                    _p.Text += " -[PanelInfo]";
            //                    _p.SelectedObject = _job._PanelInfo;
            //                    break;

            //                case "_samplingglassreasoncode":
            //                    _p.Text += " -[SamplingGlassReasonCode]";
            //                    _p.SelectedObject = _job._SamplingGlassReasonCode;
            //                    break;
            //                case "_maskrepair":
            //                    _p.Text += " -[MaskRepair]";
            //                    _p.SelectedObject = _job._MaskRepair;
            //                    break;
            //                case "_ngmarkinfo":
            //                    _p.Text += " -[NGMarkInfo]";
            //                    _p.SelectedObject = _job._NGMarkInfo;
            //                    break;
            //                case "jobprocessflows":
            //                    _p.Text += " -[JobProcessFlows]";
            //                    _p.SelectedObject = _job.JobProcessFlows;
            //                    break;
            //                case "defectcodes":
            //                    _p.Text += " -[DefectCodes]";
            //                    _p.SelectedObject = _job.DefectCodes;
            //                    break;

            //                default:
            //                    _p.SelectedObject = _job;
            //                    break;
            //            };
            //            _p.Show();
            //        }
            //        #endregion
            //        break;
            //}
        }

        private void RadioButton_CheckedChanged(object sender, EventArgs e) {
            ((RadioButton)sender).ForeColor = (((RadioButton)sender).Checked ? Color.DarkRed : Color.Black);
            //MessageBox.Show(string.Format("{0}::RadioButton_CheckedChanged", ((RadioButton)sender).Text));
        }

        private void RadioButton_Click(object sender, EventArgs e) {
            //MessageBox.Show(string.Format("{0}::RadioButton_Click", ((RadioButton)sender).Text));

            gbKey.Visible = false;
            txtKey.Text = string.Empty;

            gbKeepData.Tag = (RadioButton)sender;
            GetDataGrid();
        }

        private void cmdRefresh_Click(object sender, EventArgs e) {
            GetDataGrid();
        }
        void GetDataGrid() {
            GetDataGrid(string.Empty);
        }
        void GetDataGrid(string _key = "") {
            _dgvDataGrid.DataSource = null;//此行是必要的, 因DataGridView的欄位問題

            _dgvDetailGrid.DataSource = null;//此行是必要的, 因DataGridView的欄位問題


            Object _obj = null;
            string _name = string.Empty, _name2 = string.Empty;
            switch (((RadioButton)gbKeepData.Tag).Text) {
                default: break;
                case "Line":
                    gbKey.Visible = false;
                    _dgvDetailGrid.Visible = false;
                    _obj = ObjectManager.LineManager;
                    _name = "Line";
                    break;
                case "Node":
                    lblKey.Text = "NodeNo";
                    gbKey.Visible = true;
                    _dgvDetailGrid.Visible = false;
                    _obj = ObjectManager.EquipmentManager;
                    _name = "Node";
                    break;
                case "Port":
                    lblKey.Text = "PortID";
                    gbKey.Visible = true;
                    _dgvDetailGrid.Visible = false;
                    _obj = ObjectManager.PortManager;
                    _name = "Port";
                    break;
                case "Port Cassette":
                    lblKey.Text = "PortID";
                    gbKey.Visible = true;
                    _dgvDetailGrid.Visible = false;
                    _obj = ObjectManager.PortManager;
                    _name = "PortCassette";
                    break;
                case "Cassette Data":
                    lblKey.Text = "CSTID";
                    gbKey.Visible = true;
                    _dgvDetailGrid.Visible = false;
                    _obj = ObjectManager.CassetteManager;
                    _name = "CassetteData";
                    break;
                case "Group No":
                    lblKey.Text = ((RadioButton)gbKeepData.Tag).Text;
                    gbKey.Visible = true;
                    _dgvDetailGrid.Visible = false;
                    _obj = ObjectManager.LineManager;
                    _name = "GroupNo";
                    break;
                case "NG CRT No":
                    lblKey.Text = ((RadioButton)gbKeepData.Tag).Text;
                    gbKey.Visible = true;
                    _dgvDetailGrid.Visible = false;
                    _obj = ObjectManager.LineManager;
                    _name = "NGCrtNo";
                    break;
                case "Work WIP":
                    lblKey.Text = "GLSNO";
                    gbKey.Visible = true;
                    _dgvDetailGrid.Visible = true;
                    _obj = ObjectManager.JobManager;
                    _name = "WorkWIP";
                    break;
                case "Work Trash":
                    lblKey.Text = "GLSNO";
                    gbKey.Visible = true;
                    _dgvDetailGrid.Visible = true;
                    _obj = ObjectManager.JobManager;
                    _name = "WorkTrash";
                    break;
                case "Material":
                    lblKey.Text = "MTLID";
                    gbKey.Visible = true;
                    _dgvDetailGrid.Visible = false;
                    _obj = ObjectManager.MaterialManager;
                    _name = "Materials";
                    break;
                case "RTC Info":
                    lblKey.Text = "NodeNo";
                    gbKey.Visible = true;
                    _dgvDetailGrid.Visible = false;
                    _obj = ObjectManager.InitialCheckManager;
                    _name = "InitialChecks";
                    break;
                case "RTC insp Func Info":
                    lblKey.Text = "GlassNo";
                    gbKey.Visible = true;
                    _dgvDetailGrid.Visible = false;
                    _obj = ObjectManager.InitialCheckInspFunctionManager;
                    _name = "InitialCheckInspFunctions";
                    break;
                case "Alarm":
                    lblKey.Text = "NodeNo";
                    gbKey.Visible = true;
                    _dgvDetailGrid.Visible = false;
                    _obj = ObjectManager.AlarmManager;
                    _name = "Alarm";
                    break;
                case "Batch and Sampling":
                    lblKey.Text = "BatchID";
                    gbKey.Visible = true;
                    _dgvDetailGrid.Visible = true;
                    _obj = ObjectManager.SamplingManager;
                    _name = "BatchSampling";
                    break;
                case "Recipe Commands":
                    //v1.0.0.13-6
                    lblKey.Text = "RecipeID";
                    gbKey.Visible = true;
                    _dgvDetailGrid.Visible = false;
                    _obj = ObjectManager.RecipeManager;
                    _name = "RecipeCommands";
                    break;
                case "HS Info":
                    lblKey.Text = "TrxName";
                    gbKey.Visible = true;
                    _dgvDetailGrid.Visible = false;
                    _obj = ObjectManager.HSInfoManager;
                    _name = "HSInfoManager";
                    break;
                case "Using Group":
                    //v1.0.0.107-6
                    lblKey.Text = "TrxName";
                    gbKey.Visible = true;
                    _dgvDetailGrid.Visible = false;
                    _obj = ObjectManager.LineManager;
                    _name = "UsingGroup";
                    break;
                case "RTC Parameter Info":
                    //v1.0.0.107-6
                    lblKey.Text = "TrxName";
                    gbKey.Visible = true;
                    _dgvDetailGrid.Visible = false;
                    _obj = ObjectManager.InitialCheckParameterManager;
                    _name = "InitialCheckParameters";
                    break;
                case "SHOP Change Info":
                    //v1.0.0.107-6
                    lblKey.Text = "ShopChangeProcess";
                    gbKey.Visible = true;
                    _dgvDetailGrid.Visible = false;
                    _obj = ObjectManager.LineManager;
                    _name = "ShopChangeInfo";
                    break;
                case "Bridge CST":
                    lblKey.Text = "CSTID";
                    gbKey.Visible = true;
                    _dgvDetailGrid.Visible = false;
                    _obj = ObjectManager.BridgeCSTManager;
                    _name = "BridgeCSTManager";
                    break;
            }

            if (_dgvDetailGrid.Visible) {
                _dgvDataGrid.Width = _dgvDetailGrid.Left;
            } else {
                _dgvDataGrid.Width = _dgvDetailGrid.Left + _dgvDetailGrid.Width;
            }

            DataTable dt = ((IDataSource)_obj).GetDataTable(_name, txtKey.Text);
            if (dt != null) _dgvDataGrid.DataSource = dt;
        }

        private void _dgvDataGrid_RowStateChanged(object sender, DataGridViewRowStateChangedEventArgs e) {

        }

        private void _dgvDataGrid_RowEnter(object sender, DataGridViewCellEventArgs e) {
            string _name = string.Empty;
            _dgvDetailGrid.DataSource = null;//此行是必要的, 因DataGridView的欄位問題

            if (_dgvDataGrid.DataSource == null) return;
            DataTable dt = (DataTable)_dgvDataGrid.DataSource;
            if (_dgvDetailGrid.Visible) {
                if (dt.Rows.Count > 0) {
                    Object _obj = null;
                    switch (((RadioButton)gbKeepData.Tag).Text) {
                        case "Work WIP":
                        case "Work Trash":
                            _obj = ObjectManager.JobManager;
                            _name = string.Format("DETAIL|{0}", dt.Rows[e.RowIndex]["_GlsNo"].ToString());
                            break;
                        case "Batch and Sampling":
                             _obj = ObjectManager.SamplingManager;
                             _name = string.Format("DETAIL|{0}", dt.Rows[e.RowIndex]["_BatchID"].ToString());
                            break;
                    }
                    if (_name.NullOrEmpty()) return;
                    DataTable dt2 = ((IDataSource)_obj).GetDataTable(_name, txtKey.Text);
                    if (dt2 != null) _dgvDetailGrid.DataSource = dt2;
                }
            }
        }

        private void lblKey_MouseDoubleClick(object sender, MouseEventArgs e) {

        }

        private void lblKey_Click(object sender, EventArgs e) {
            if ((rbWorkWIP.Checked || rbWorkTrash.Checked) && lblKey.Text == "GLSNO" && ModifierKeys == Keys.Control) {
                txtKey.Text = string.Empty;
                txtCstSeq.Text = string.Empty;
                txtCstSeq.Focus();
                gbManualRemoveJobViaCstSeq.Visible = !gbManualRemoveJobViaCstSeq.Visible;
            }
            if ((rbRecipeCommands.Checked) && lblKey.Text == "RecipeID" && ModifierKeys == Keys.Control) {
                if (!_dgvDataGrid.NullOrEmpty() && !_dgvDataGrid.CurrentRow.NullOrEmpty() && !_dgvDataGrid.CurrentRow.Cells[0].Value.ToString().NullOrEmpty()) {
                    //ObjectManager.RecipeManager.DeleteRecipeCheckCmd(_dgvDataGrid.CurrentRow.Cells[0].Value.ToString());//v1.0.0.155-7
                    ObjectManager.RecipeManager.ForceDeleteRecipeCheckCmd(_dgvDataGrid.CurrentRow.Cells[0].Value.ToString());//v1.0.0.155-7
                }
            }
            if (rbBatchSampling.Checked && lblKey.Text == "BatchID" && ModifierKeys == Keys.Control) {
                if (!_dgvDataGrid.NullOrEmpty() && !_dgvDataGrid.CurrentRow.NullOrEmpty() && !_dgvDataGrid.CurrentRow.Cells[0].Value.ToString().NullOrEmpty()) {
                    ObjectManager.SamplingManager.BatchEnd(_dgvDataGrid.CurrentRow.Cells[1].Value.ToString(), true);
                }
            }
            if (rbBatchSampling.Checked && lblKey.Text == "BatchID" && ModifierKeys == Keys.Alt) {
                if (!_dgvDataGrid.NullOrEmpty() && !_dgvDataGrid.CurrentRow.NullOrEmpty() && !_dgvDataGrid.CurrentRow.Cells[0].Value.ToString().NullOrEmpty()) {
                    ObjectManager.SamplingManager.BatchEnd(_dgvDataGrid.CurrentRow.Cells[1].Value.ToString(), true, true); 
                }
            }
            if (rbBatchSampling.Checked && lblKey.Text == "BatchID" && ModifierKeys == Keys.Shift) {
                if (!_dgvDataGrid.NullOrEmpty() && !_dgvDataGrid.CurrentRow.NullOrEmpty() && !_dgvDataGrid.CurrentRow.Cells[0].Value.ToString().NullOrEmpty()) {
                    ObjectManager.SamplingManager.SamplingEnd(_dgvDataGrid.CurrentRow.Cells[1].Value.ToString(), true);
                }
            }
            if (rbBatchSampling.Checked && lblKey.Text == "BatchID" && ModifierKeys == (Keys.Shift | Keys.Control)) {
                //v1.0.0.41-2
                if (!_dgvDataGrid.NullOrEmpty() && !_dgvDataGrid.CurrentRow.NullOrEmpty() && !_dgvDataGrid.CurrentRow.Cells[0].Value.ToString().NullOrEmpty()) {
                    ObjectManager.SamplingManager.BatchErase(_dgvDataGrid.CurrentRow.Cells[1].Value.ToString(), true);
                }
            }
            //[Start]v1.0.0.55-2
            if ((rbMaterial.Checked) && lblKey.Text == "MTLID" && ModifierKeys == Keys.Control)
            {
                //[Start]v1.0.0.60-14
                if (!_dgvDataGrid.NullOrEmpty() && !_dgvDataGrid.CurrentRow.NullOrEmpty() && !_dgvDataGrid.CurrentRow.Cells[0].Value.ToString().NullOrEmpty())
                {
                    ObjectManager.MaterialManager.UnMountMaterial(_dgvDataGrid.CurrentRow.Cells[0].Value.ToString().ToTrim(), _dgvDataGrid.CurrentRow.Cells[4].Value.ToString().ToTrim().Substring(0, _dgvDataGrid.CurrentRow.Cells[4].Value.ToString().ToTrim().IndexOf("::")), _dgvDataGrid.CurrentRow.Cells[1].Value.ToString().ToTrim());
                }
                //[End]v1.0.0.60-14
            }
            //[End]v1.0.0.55-2

            //[Start]v1.0.0.113-7
            if (rbRTCInfo.Checked && lblKey.Text == "NodeNo" && ModifierKeys == (Keys.Control))
            {
                if (!_dgvDataGrid.NullOrEmpty() && !_dgvDataGrid.CurrentRow.NullOrEmpty() && !_dgvDataGrid.CurrentRow.Cells[0].Value.ToString().NullOrEmpty() && !_dgvDataGrid.CurrentRow.Cells[1].Value.ToString().NullOrEmpty())
                {
                    ObjectManager.InitialCheckManager.DeleteInitialCheck(_dgvDataGrid.CurrentRow.Cells[0].Value.ToString().Trim(), _dgvDataGrid.CurrentRow.Cells[1].Value.ToString().Trim());
                }
            }

            if (rbRTCInspFuncInfo.Checked && lblKey.Text == "GlassNo" && ModifierKeys == (Keys.Control))
            {
                if (!_dgvDataGrid.NullOrEmpty() && !_dgvDataGrid.CurrentRow.NullOrEmpty() && !_dgvDataGrid.CurrentRow.Cells[0].Value.ToString().NullOrEmpty() && !_dgvDataGrid.CurrentRow.Cells[1].Value.ToString().NullOrEmpty())
                {
                    ObjectManager.InitialCheckInspFunctionManager.DeleteInitialCheckInspFunction(_dgvDataGrid.CurrentRow.Cells[0].Value.ToString().Trim(), _dgvDataGrid.CurrentRow.Cells[1].Value.ToString().Trim());
                }
            }
            //[End]v1.0.0.113-7
            if (rbHSInfo.Checked && lblKey.Text == "TrxName" && ModifierKeys == (Keys.Control))
            {
                if (!_dgvDataGrid.NullOrEmpty() && !_dgvDataGrid.CurrentRow.NullOrEmpty() && !_dgvDataGrid.CurrentRow.Cells[1].Value.ToString().NullOrEmpty())
                {
                    ObjectManager.HSInfoManager.DeleteHSInfoEntity(_dgvDataGrid.CurrentRow.Cells[1].Value.ToString().Trim());
                }
            }

            if (rbShopChangeInfo.Checked && lblKey.Text == "ShopChangeProcess" && ModifierKeys == (Keys.Control))
            {
                if (!_dgvDataGrid.NullOrEmpty() && !_dgvDataGrid.CurrentRow.NullOrEmpty())
                {
                    if(Workbench.Instance.RunningFromIDE()) ObjectManager.LineManager.SetMQName("CF8B");
                    ObjectManager.LineManager.ShopChangeInfoSetDefault();
                }
            }
        }

        private void btnManualRemoveJobViaCstSeq_Click(object sender, EventArgs e) {
            if (!txtCstSeq.Text.NullOrEmpty()) {
                ObjectManager.JobManager.ManualRemoveJobsViaCstSeq(txtCstSeq.Text.Parse());
            } else {
                if (!txtKey.NullOrEmpty()) ObjectManager.JobManager.ManualRemoveJobsViaCstSeq(txtKey.Text.ToUpperTrim());
            }

            txtKey.Text = string.Empty;
            txtKey.Focus();
            gbManualRemoveJobViaCstSeq.Visible = false;
            cmdRefresh_Click(sender, e);
        }

        private void rbBatchSampling_MouseClick(object sender, MouseEventArgs e) {
            if (rbBatchSampling.Checked && lblKey.Text == "BatchID" && ModifierKeys == Keys.Control) {
                if (lblMemo.Text.ToTrim() == string.Empty) {
                    //lblMemo.Text = "Ctrl=Batch / Shift=Sampling / Ctrl+Shift=Erase"; //v1.0.0.41-2
                    lblMemo.Text = "Ctrl=Batch/Alt=Generated/Shift=Sampling/Ctrl+Shift=Erase"; //v1.0.0.50-3
                } else {
                    lblMemo.Text = string.Empty;
                }
            }
        }
    }
}
