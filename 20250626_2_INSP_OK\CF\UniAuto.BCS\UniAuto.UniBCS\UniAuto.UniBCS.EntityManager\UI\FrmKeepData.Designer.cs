﻿namespace UniAuto.UniBCS.EntityManager.UI
{
    partial class FrmKeepData
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.panel7 = new System.Windows.Forms.Panel();
            this._dgvDetailGrid = new System.Windows.Forms.DataGridView();
            this._dgvDataGrid = new System.Windows.Forms.DataGridView();
            this.panel4 = new System.Windows.Forms.Panel();
            this.panel3 = new System.Windows.Forms.Panel();
            this.panel2 = new System.Windows.Forms.Panel();
            this.pnlMain = new System.Windows.Forms.Panel();
            this.panel9 = new System.Windows.Forms.Panel();
            this.panel8 = new System.Windows.Forms.Panel();
            this.cmdRefresh = new System.Windows.Forms.Button();
            this.gbKeepData = new System.Windows.Forms.GroupBox();
            this.rbShopChangeInfo = new System.Windows.Forms.RadioButton();
            this.rbRTCParameterInfo = new System.Windows.Forms.RadioButton();
            this.rbUsingGroup = new System.Windows.Forms.RadioButton();
            this.rbHSInfo = new System.Windows.Forms.RadioButton();
            this.rbRTCInspFuncInfo = new System.Windows.Forms.RadioButton();
            this.rbRTCInfo = new System.Windows.Forms.RadioButton();
            this.lblMemo = new System.Windows.Forms.Label();
            this.rbRecipeCommands = new System.Windows.Forms.RadioButton();
            this.rbBatchSampling = new System.Windows.Forms.RadioButton();
            this.gbKey = new System.Windows.Forms.GroupBox();
            this.txtKey = new System.Windows.Forms.TextBox();
            this.lblKey = new System.Windows.Forms.Label();
            this.gbManualRemoveJobViaCstSeq = new System.Windows.Forms.GroupBox();
            this.btnManualRemoveJobViaCstSeq = new System.Windows.Forms.Button();
            this.txtCstSeq = new System.Windows.Forms.TextBox();
            this.label1 = new System.Windows.Forms.Label();
            this.rbMaterial = new System.Windows.Forms.RadioButton();
            this.rbAlarm = new System.Windows.Forms.RadioButton();
            this.rbWorkTrash = new System.Windows.Forms.RadioButton();
            this.rbWorkWIP = new System.Windows.Forms.RadioButton();
            this.rbNGCrtNo = new System.Windows.Forms.RadioButton();
            this.rbGroupNo = new System.Windows.Forms.RadioButton();
            this.rbCstData = new System.Windows.Forms.RadioButton();
            this.rbPortCst = new System.Windows.Forms.RadioButton();
            this.rbPort = new System.Windows.Forms.RadioButton();
            this.rbNode = new System.Windows.Forms.RadioButton();
            this.rbLine = new System.Windows.Forms.RadioButton();
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.deleteToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem2 = new System.Windows.Forms.ToolStripSeparator();
            this.removeToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.recoveryToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem1 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem3 = new System.Windows.Forms.ToolStripSeparator();
            this.refreshToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.contextMenuStrip2 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.editToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.rbBridgeCST = new System.Windows.Forms.RadioButton();
            this.panel7.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this._dgvDetailGrid)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this._dgvDataGrid)).BeginInit();
            this.pnlMain.SuspendLayout();
            this.panel9.SuspendLayout();
            this.panel8.SuspendLayout();
            this.gbKeepData.SuspendLayout();
            this.gbKey.SuspendLayout();
            this.gbManualRemoveJobViaCstSeq.SuspendLayout();
            this.contextMenuStrip1.SuspendLayout();
            this.contextMenuStrip2.SuspendLayout();
            this.SuspendLayout();
            // 
            // panel7
            // 
            this.panel7.BackColor = System.Drawing.Color.Transparent;
            this.panel7.Controls.Add(this._dgvDetailGrid);
            this.panel7.Controls.Add(this._dgvDataGrid);
            this.panel7.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panel7.Location = new System.Drawing.Point(0, 0);
            this.panel7.Name = "panel7";
            this.panel7.Size = new System.Drawing.Size(998, 542);
            this.panel7.TabIndex = 14;
            // 
            // _dgvDetailGrid
            // 
            this._dgvDetailGrid.AllowUserToAddRows = false;
            this._dgvDetailGrid.AllowUserToDeleteRows = false;
            this._dgvDetailGrid.BackgroundColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this._dgvDetailGrid.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this._dgvDetailGrid.Dock = System.Windows.Forms.DockStyle.Right;
            this._dgvDetailGrid.EditMode = System.Windows.Forms.DataGridViewEditMode.EditProgrammatically;
            this._dgvDetailGrid.Location = new System.Drawing.Point(691, 0);
            this._dgvDetailGrid.Name = "_dgvDetailGrid";
            this._dgvDetailGrid.RowTemplate.Height = 27;
            this._dgvDetailGrid.Size = new System.Drawing.Size(307, 542);
            this._dgvDetailGrid.TabIndex = 1;
            this._dgvDetailGrid.Visible = false;
            // 
            // _dgvDataGrid
            // 
            this._dgvDataGrid.AllowUserToAddRows = false;
            this._dgvDataGrid.AllowUserToDeleteRows = false;
            this._dgvDataGrid.BackgroundColor = System.Drawing.Color.FromArgb(((int)(((byte)(189)))), ((int)(((byte)(216)))), ((int)(((byte)(243)))));
            this._dgvDataGrid.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this._dgvDataGrid.Dock = System.Windows.Forms.DockStyle.Left;
            this._dgvDataGrid.EditMode = System.Windows.Forms.DataGridViewEditMode.EditProgrammatically;
            this._dgvDataGrid.Location = new System.Drawing.Point(0, 0);
            this._dgvDataGrid.Name = "_dgvDataGrid";
            this._dgvDataGrid.RowTemplate.Height = 27;
            this._dgvDataGrid.Size = new System.Drawing.Size(691, 542);
            this._dgvDataGrid.TabIndex = 0;
            this._dgvDataGrid.CellContentDoubleClick += new System.Windows.Forms.DataGridViewCellEventHandler(this._dgvDataGird_CellContentDoubleClick);
            this._dgvDataGrid.RowEnter += new System.Windows.Forms.DataGridViewCellEventHandler(this._dgvDataGrid_RowEnter);
            this._dgvDataGrid.RowStateChanged += new System.Windows.Forms.DataGridViewRowStateChangedEventHandler(this._dgvDataGrid_RowStateChanged);
            this._dgvDataGrid.MouseClick += new System.Windows.Forms.MouseEventHandler(this._dgvDataGird_MouseClick);
            this._dgvDataGrid.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this._dgvDataGird_MouseDoubleClick);
            // 
            // panel4
            // 
            this.panel4.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(47)))), ((int)(((byte)(109)))), ((int)(((byte)(158)))));
            this.panel4.Dock = System.Windows.Forms.DockStyle.Right;
            this.panel4.Location = new System.Drawing.Point(1011, 0);
            this.panel4.Name = "panel4";
            this.panel4.Size = new System.Drawing.Size(13, 644);
            this.panel4.TabIndex = 12;
            // 
            // panel3
            // 
            this.panel3.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(47)))), ((int)(((byte)(109)))), ((int)(((byte)(158)))));
            this.panel3.Dock = System.Windows.Forms.DockStyle.Left;
            this.panel3.Location = new System.Drawing.Point(0, 0);
            this.panel3.Name = "panel3";
            this.panel3.Size = new System.Drawing.Size(13, 644);
            this.panel3.TabIndex = 11;
            // 
            // panel2
            // 
            this.panel2.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(47)))), ((int)(((byte)(109)))), ((int)(((byte)(158)))));
            this.panel2.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.panel2.Location = new System.Drawing.Point(0, 644);
            this.panel2.Name = "panel2";
            this.panel2.Size = new System.Drawing.Size(1024, 10);
            this.panel2.TabIndex = 10;
            // 
            // pnlMain
            // 
            this.pnlMain.BackColor = System.Drawing.Color.Transparent;
            this.pnlMain.Controls.Add(this.panel9);
            this.pnlMain.Controls.Add(this.panel8);
            this.pnlMain.Controls.Add(this.panel4);
            this.pnlMain.Controls.Add(this.panel3);
            this.pnlMain.Controls.Add(this.panel2);
            this.pnlMain.Dock = System.Windows.Forms.DockStyle.Fill;
            this.pnlMain.Location = new System.Drawing.Point(0, 0);
            this.pnlMain.Name = "pnlMain";
            this.pnlMain.Size = new System.Drawing.Size(1024, 654);
            this.pnlMain.TabIndex = 2;
            // 
            // panel9
            // 
            this.panel9.Controls.Add(this.panel7);
            this.panel9.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panel9.Location = new System.Drawing.Point(13, 102);
            this.panel9.Name = "panel9";
            this.panel9.Size = new System.Drawing.Size(998, 542);
            this.panel9.TabIndex = 16;
            // 
            // panel8
            // 
            this.panel8.Controls.Add(this.cmdRefresh);
            this.panel8.Controls.Add(this.gbKeepData);
            this.panel8.Dock = System.Windows.Forms.DockStyle.Top;
            this.panel8.Location = new System.Drawing.Point(13, 0);
            this.panel8.Name = "panel8";
            this.panel8.Size = new System.Drawing.Size(998, 102);
            this.panel8.TabIndex = 15;
            // 
            // cmdRefresh
            // 
            this.cmdRefresh.Dock = System.Windows.Forms.DockStyle.Fill;
            this.cmdRefresh.Location = new System.Drawing.Point(917, 0);
            this.cmdRefresh.Name = "cmdRefresh";
            this.cmdRefresh.Size = new System.Drawing.Size(81, 102);
            this.cmdRefresh.TabIndex = 3;
            this.cmdRefresh.Text = "Refresh";
            this.cmdRefresh.UseVisualStyleBackColor = true;
            this.cmdRefresh.Click += new System.EventHandler(this.cmdRefresh_Click);
            // 
            // gbKeepData
            // 
            this.gbKeepData.Controls.Add(this.rbBridgeCST);
            this.gbKeepData.Controls.Add(this.rbShopChangeInfo);
            this.gbKeepData.Controls.Add(this.rbRTCParameterInfo);
            this.gbKeepData.Controls.Add(this.rbUsingGroup);
            this.gbKeepData.Controls.Add(this.rbHSInfo);
            this.gbKeepData.Controls.Add(this.rbRTCInspFuncInfo);
            this.gbKeepData.Controls.Add(this.rbRTCInfo);
            this.gbKeepData.Controls.Add(this.lblMemo);
            this.gbKeepData.Controls.Add(this.rbRecipeCommands);
            this.gbKeepData.Controls.Add(this.rbBatchSampling);
            this.gbKeepData.Controls.Add(this.gbKey);
            this.gbKeepData.Controls.Add(this.gbManualRemoveJobViaCstSeq);
            this.gbKeepData.Controls.Add(this.rbMaterial);
            this.gbKeepData.Controls.Add(this.rbAlarm);
            this.gbKeepData.Controls.Add(this.rbWorkTrash);
            this.gbKeepData.Controls.Add(this.rbWorkWIP);
            this.gbKeepData.Controls.Add(this.rbNGCrtNo);
            this.gbKeepData.Controls.Add(this.rbGroupNo);
            this.gbKeepData.Controls.Add(this.rbCstData);
            this.gbKeepData.Controls.Add(this.rbPortCst);
            this.gbKeepData.Controls.Add(this.rbPort);
            this.gbKeepData.Controls.Add(this.rbNode);
            this.gbKeepData.Controls.Add(this.rbLine);
            this.gbKeepData.Dock = System.Windows.Forms.DockStyle.Left;
            this.gbKeepData.Location = new System.Drawing.Point(0, 0);
            this.gbKeepData.Name = "gbKeepData";
            this.gbKeepData.Size = new System.Drawing.Size(917, 102);
            this.gbKeepData.TabIndex = 2;
            this.gbKeepData.TabStop = false;
            this.gbKeepData.Text = "Keep Data";
            // 
            // rbShopChangeInfo
            // 
            this.rbShopChangeInfo.AutoSize = true;
            this.rbShopChangeInfo.Location = new System.Drawing.Point(571, 60);
            this.rbShopChangeInfo.Name = "rbShopChangeInfo";
            this.rbShopChangeInfo.Size = new System.Drawing.Size(113, 16);
            this.rbShopChangeInfo.TabIndex = 21;
            this.rbShopChangeInfo.TabStop = true;
            this.rbShopChangeInfo.Text = "SHOP Change Info";
            this.rbShopChangeInfo.UseVisualStyleBackColor = true;
            this.rbShopChangeInfo.CheckedChanged += new System.EventHandler(this.RadioButton_CheckedChanged);
            this.rbShopChangeInfo.Click += new System.EventHandler(this.RadioButton_Click);
            // 
            // rbRTCParameterInfo
            // 
            this.rbRTCParameterInfo.AutoSize = true;
            this.rbRTCParameterInfo.Location = new System.Drawing.Point(181, 60);
            this.rbRTCParameterInfo.Name = "rbRTCParameterInfo";
            this.rbRTCParameterInfo.Size = new System.Drawing.Size(118, 16);
            this.rbRTCParameterInfo.TabIndex = 20;
            this.rbRTCParameterInfo.TabStop = true;
            this.rbRTCParameterInfo.Text = "RTC Parameter Info";
            this.rbRTCParameterInfo.UseVisualStyleBackColor = true;
            this.rbRTCParameterInfo.CheckedChanged += new System.EventHandler(this.RadioButton_CheckedChanged);
            this.rbRTCParameterInfo.Click += new System.EventHandler(this.RadioButton_Click);
            // 
            // rbUsingGroup
            // 
            this.rbUsingGroup.AutoSize = true;
            this.rbUsingGroup.Location = new System.Drawing.Point(452, 60);
            this.rbUsingGroup.Name = "rbUsingGroup";
            this.rbUsingGroup.Size = new System.Drawing.Size(83, 16);
            this.rbUsingGroup.TabIndex = 19;
            this.rbUsingGroup.TabStop = true;
            this.rbUsingGroup.Text = "Using Group";
            this.rbUsingGroup.UseVisualStyleBackColor = true;
            this.rbUsingGroup.CheckedChanged += new System.EventHandler(this.RadioButton_CheckedChanged);
            this.rbUsingGroup.Click += new System.EventHandler(this.RadioButton_Click);
            // 
            // rbHSInfo
            // 
            this.rbHSInfo.AutoSize = true;
            this.rbHSInfo.Location = new System.Drawing.Point(359, 60);
            this.rbHSInfo.Name = "rbHSInfo";
            this.rbHSInfo.Size = new System.Drawing.Size(60, 16);
            this.rbHSInfo.TabIndex = 18;
            this.rbHSInfo.TabStop = true;
            this.rbHSInfo.Text = "HS Info";
            this.rbHSInfo.UseVisualStyleBackColor = true;
            this.rbHSInfo.CheckedChanged += new System.EventHandler(this.RadioButton_CheckedChanged);
            this.rbHSInfo.Click += new System.EventHandler(this.RadioButton_Click);
            // 
            // rbRTCInspFuncInfo
            // 
            this.rbRTCInspFuncInfo.AutoSize = true;
            this.rbRTCInspFuncInfo.Location = new System.Drawing.Point(6, 60);
            this.rbRTCInspFuncInfo.Name = "rbRTCInspFuncInfo";
            this.rbRTCInspFuncInfo.Size = new System.Drawing.Size(117, 16);
            this.rbRTCInspFuncInfo.TabIndex = 17;
            this.rbRTCInspFuncInfo.TabStop = true;
            this.rbRTCInspFuncInfo.Text = "RTC insp Func Info";
            this.rbRTCInspFuncInfo.UseVisualStyleBackColor = true;
            this.rbRTCInspFuncInfo.CheckedChanged += new System.EventHandler(this.RadioButton_CheckedChanged);
            this.rbRTCInspFuncInfo.Click += new System.EventHandler(this.RadioButton_Click);
            // 
            // rbRTCInfo
            // 
            this.rbRTCInfo.AutoSize = true;
            this.rbRTCInfo.Location = new System.Drawing.Point(290, 36);
            this.rbRTCInfo.Name = "rbRTCInfo";
            this.rbRTCInfo.Size = new System.Drawing.Size(69, 16);
            this.rbRTCInfo.TabIndex = 16;
            this.rbRTCInfo.TabStop = true;
            this.rbRTCInfo.Text = "RTC Info";
            this.rbRTCInfo.UseVisualStyleBackColor = true;
            this.rbRTCInfo.CheckedChanged += new System.EventHandler(this.RadioButton_CheckedChanged);
            this.rbRTCInfo.Click += new System.EventHandler(this.RadioButton_Click);
            // 
            // lblMemo
            // 
            this.lblMemo.AutoSize = true;
            this.lblMemo.Font = new System.Drawing.Font("Tahoma", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblMemo.ForeColor = System.Drawing.Color.DarkRed;
            this.lblMemo.Location = new System.Drawing.Point(605, 57);
            this.lblMemo.Name = "lblMemo";
            this.lblMemo.Size = new System.Drawing.Size(22, 13);
            this.lblMemo.TabIndex = 15;
            this.lblMemo.Text = "     ";
            // 
            // rbRecipeCommands
            // 
            this.rbRecipeCommands.AutoSize = true;
            this.rbRecipeCommands.Location = new System.Drawing.Point(568, 17);
            this.rbRecipeCommands.Name = "rbRecipeCommands";
            this.rbRecipeCommands.Size = new System.Drawing.Size(111, 16);
            this.rbRecipeCommands.TabIndex = 14;
            this.rbRecipeCommands.TabStop = true;
            this.rbRecipeCommands.Text = "Recipe Commands";
            this.rbRecipeCommands.UseVisualStyleBackColor = true;
            this.rbRecipeCommands.CheckedChanged += new System.EventHandler(this.RadioButton_CheckedChanged);
            this.rbRecipeCommands.Click += new System.EventHandler(this.RadioButton_Click);
            // 
            // rbBatchSampling
            // 
            this.rbBatchSampling.AutoSize = true;
            this.rbBatchSampling.Location = new System.Drawing.Point(479, 36);
            this.rbBatchSampling.Name = "rbBatchSampling";
            this.rbBatchSampling.Size = new System.Drawing.Size(117, 16);
            this.rbBatchSampling.TabIndex = 13;
            this.rbBatchSampling.TabStop = true;
            this.rbBatchSampling.Tag = "Ctrl=Batch / Shift=Sampling";
            this.rbBatchSampling.Text = "Batch and Sampling";
            this.rbBatchSampling.UseVisualStyleBackColor = true;
            this.rbBatchSampling.CheckedChanged += new System.EventHandler(this.RadioButton_CheckedChanged);
            this.rbBatchSampling.Click += new System.EventHandler(this.RadioButton_Click);
            this.rbBatchSampling.MouseClick += new System.Windows.Forms.MouseEventHandler(this.rbBatchSampling_MouseClick);
            // 
            // gbKey
            // 
            this.gbKey.Controls.Add(this.txtKey);
            this.gbKey.Controls.Add(this.lblKey);
            this.gbKey.Location = new System.Drawing.Point(715, 3);
            this.gbKey.Name = "gbKey";
            this.gbKey.Size = new System.Drawing.Size(197, 44);
            this.gbKey.TabIndex = 10;
            this.gbKey.TabStop = false;
            this.gbKey.Visible = false;
            // 
            // txtKey
            // 
            this.txtKey.Location = new System.Drawing.Point(67, 15);
            this.txtKey.Name = "txtKey";
            this.txtKey.Size = new System.Drawing.Size(124, 22);
            this.txtKey.TabIndex = 1;
            // 
            // lblKey
            // 
            this.lblKey.AutoSize = true;
            this.lblKey.Location = new System.Drawing.Point(6, 19);
            this.lblKey.Name = "lblKey";
            this.lblKey.Size = new System.Drawing.Size(45, 12);
            this.lblKey.TabIndex = 0;
            this.lblKey.Text = "XXXXX";
            this.lblKey.Click += new System.EventHandler(this.lblKey_Click);
            this.lblKey.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.lblKey_MouseDoubleClick);
            // 
            // gbManualRemoveJobViaCstSeq
            // 
            this.gbManualRemoveJobViaCstSeq.Controls.Add(this.btnManualRemoveJobViaCstSeq);
            this.gbManualRemoveJobViaCstSeq.Controls.Add(this.txtCstSeq);
            this.gbManualRemoveJobViaCstSeq.Controls.Add(this.label1);
            this.gbManualRemoveJobViaCstSeq.Location = new System.Drawing.Point(715, 41);
            this.gbManualRemoveJobViaCstSeq.Name = "gbManualRemoveJobViaCstSeq";
            this.gbManualRemoveJobViaCstSeq.Size = new System.Drawing.Size(197, 44);
            this.gbManualRemoveJobViaCstSeq.TabIndex = 12;
            this.gbManualRemoveJobViaCstSeq.TabStop = false;
            this.gbManualRemoveJobViaCstSeq.Visible = false;
            // 
            // btnManualRemoveJobViaCstSeq
            // 
            this.btnManualRemoveJobViaCstSeq.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.btnManualRemoveJobViaCstSeq.Location = new System.Drawing.Point(144, 12);
            this.btnManualRemoveJobViaCstSeq.Name = "btnManualRemoveJobViaCstSeq";
            this.btnManualRemoveJobViaCstSeq.Size = new System.Drawing.Size(47, 27);
            this.btnManualRemoveJobViaCstSeq.TabIndex = 4;
            this.btnManualRemoveJobViaCstSeq.Text = "Run";
            this.btnManualRemoveJobViaCstSeq.UseVisualStyleBackColor = true;
            this.btnManualRemoveJobViaCstSeq.Click += new System.EventHandler(this.btnManualRemoveJobViaCstSeq_Click);
            // 
            // txtCstSeq
            // 
            this.txtCstSeq.Location = new System.Drawing.Point(72, 15);
            this.txtCstSeq.MaxLength = 3;
            this.txtCstSeq.Name = "txtCstSeq";
            this.txtCstSeq.Size = new System.Drawing.Size(66, 22);
            this.txtCstSeq.TabIndex = 1;
            this.txtCstSeq.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(6, 19);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(56, 12);
            this.label1.TabIndex = 0;
            this.label1.Text = "CST SEQ#";
            // 
            // rbMaterial
            // 
            this.rbMaterial.AutoSize = true;
            this.rbMaterial.Location = new System.Drawing.Point(196, 36);
            this.rbMaterial.Name = "rbMaterial";
            this.rbMaterial.Size = new System.Drawing.Size(61, 16);
            this.rbMaterial.TabIndex = 11;
            this.rbMaterial.TabStop = true;
            this.rbMaterial.Text = "Material";
            this.rbMaterial.UseVisualStyleBackColor = true;
            this.rbMaterial.CheckedChanged += new System.EventHandler(this.RadioButton_CheckedChanged);
            this.rbMaterial.Click += new System.EventHandler(this.RadioButton_Click);
            // 
            // rbAlarm
            // 
            this.rbAlarm.AutoSize = true;
            this.rbAlarm.Location = new System.Drawing.Point(402, 36);
            this.rbAlarm.Name = "rbAlarm";
            this.rbAlarm.Size = new System.Drawing.Size(52, 16);
            this.rbAlarm.TabIndex = 9;
            this.rbAlarm.TabStop = true;
            this.rbAlarm.Text = "Alarm";
            this.rbAlarm.UseVisualStyleBackColor = true;
            this.rbAlarm.CheckedChanged += new System.EventHandler(this.RadioButton_CheckedChanged);
            this.rbAlarm.Click += new System.EventHandler(this.RadioButton_Click);
            // 
            // rbWorkTrash
            // 
            this.rbWorkTrash.AutoSize = true;
            this.rbWorkTrash.Location = new System.Drawing.Point(87, 36);
            this.rbWorkTrash.Name = "rbWorkTrash";
            this.rbWorkTrash.Size = new System.Drawing.Size(79, 16);
            this.rbWorkTrash.TabIndex = 8;
            this.rbWorkTrash.TabStop = true;
            this.rbWorkTrash.Text = "Work Trash";
            this.rbWorkTrash.UseVisualStyleBackColor = true;
            this.rbWorkTrash.CheckedChanged += new System.EventHandler(this.RadioButton_CheckedChanged);
            this.rbWorkTrash.Click += new System.EventHandler(this.RadioButton_Click);
            // 
            // rbWorkWIP
            // 
            this.rbWorkWIP.AutoSize = true;
            this.rbWorkWIP.Location = new System.Drawing.Point(6, 36);
            this.rbWorkWIP.Name = "rbWorkWIP";
            this.rbWorkWIP.Size = new System.Drawing.Size(74, 16);
            this.rbWorkWIP.TabIndex = 7;
            this.rbWorkWIP.TabStop = true;
            this.rbWorkWIP.Text = "Work WIP";
            this.rbWorkWIP.UseVisualStyleBackColor = true;
            this.rbWorkWIP.CheckedChanged += new System.EventHandler(this.RadioButton_CheckedChanged);
            this.rbWorkWIP.Click += new System.EventHandler(this.RadioButton_Click);
            // 
            // rbNGCrtNo
            // 
            this.rbNGCrtNo.AutoSize = true;
            this.rbNGCrtNo.Location = new System.Drawing.Point(479, 17);
            this.rbNGCrtNo.Name = "rbNGCrtNo";
            this.rbNGCrtNo.Size = new System.Drawing.Size(82, 16);
            this.rbNGCrtNo.TabIndex = 6;
            this.rbNGCrtNo.TabStop = true;
            this.rbNGCrtNo.Text = "NG CRT No";
            this.rbNGCrtNo.UseVisualStyleBackColor = true;
            this.rbNGCrtNo.CheckedChanged += new System.EventHandler(this.RadioButton_CheckedChanged);
            this.rbNGCrtNo.Click += new System.EventHandler(this.RadioButton_Click);
            // 
            // rbGroupNo
            // 
            this.rbGroupNo.AutoSize = true;
            this.rbGroupNo.Location = new System.Drawing.Point(402, 18);
            this.rbGroupNo.Name = "rbGroupNo";
            this.rbGroupNo.Size = new System.Drawing.Size(70, 16);
            this.rbGroupNo.TabIndex = 5;
            this.rbGroupNo.TabStop = true;
            this.rbGroupNo.Text = "Group No";
            this.rbGroupNo.UseVisualStyleBackColor = true;
            this.rbGroupNo.CheckedChanged += new System.EventHandler(this.RadioButton_CheckedChanged);
            this.rbGroupNo.Click += new System.EventHandler(this.RadioButton_Click);
            // 
            // rbCstData
            // 
            this.rbCstData.AutoSize = true;
            this.rbCstData.Location = new System.Drawing.Point(290, 17);
            this.rbCstData.Name = "rbCstData";
            this.rbCstData.Size = new System.Drawing.Size(84, 16);
            this.rbCstData.TabIndex = 4;
            this.rbCstData.TabStop = true;
            this.rbCstData.Text = "Cassette Data";
            this.rbCstData.UseVisualStyleBackColor = true;
            this.rbCstData.CheckedChanged += new System.EventHandler(this.RadioButton_CheckedChanged);
            this.rbCstData.Click += new System.EventHandler(this.RadioButton_Click);
            // 
            // rbPortCst
            // 
            this.rbPortCst.AutoSize = true;
            this.rbPortCst.Location = new System.Drawing.Point(196, 17);
            this.rbPortCst.Name = "rbPortCst";
            this.rbPortCst.Size = new System.Drawing.Size(82, 16);
            this.rbPortCst.TabIndex = 3;
            this.rbPortCst.TabStop = true;
            this.rbPortCst.Text = "Port Cassette";
            this.rbPortCst.UseVisualStyleBackColor = true;
            this.rbPortCst.CheckedChanged += new System.EventHandler(this.RadioButton_CheckedChanged);
            this.rbPortCst.Click += new System.EventHandler(this.RadioButton_Click);
            // 
            // rbPort
            // 
            this.rbPort.AutoSize = true;
            this.rbPort.Location = new System.Drawing.Point(114, 17);
            this.rbPort.Name = "rbPort";
            this.rbPort.Size = new System.Drawing.Size(42, 16);
            this.rbPort.TabIndex = 2;
            this.rbPort.TabStop = true;
            this.rbPort.Text = "Port";
            this.rbPort.UseVisualStyleBackColor = true;
            this.rbPort.CheckedChanged += new System.EventHandler(this.RadioButton_CheckedChanged);
            this.rbPort.Click += new System.EventHandler(this.RadioButton_Click);
            // 
            // rbNode
            // 
            this.rbNode.AutoSize = true;
            this.rbNode.Location = new System.Drawing.Point(57, 17);
            this.rbNode.Name = "rbNode";
            this.rbNode.Size = new System.Drawing.Size(48, 16);
            this.rbNode.TabIndex = 1;
            this.rbNode.TabStop = true;
            this.rbNode.Text = "Node";
            this.rbNode.UseVisualStyleBackColor = true;
            this.rbNode.CheckedChanged += new System.EventHandler(this.RadioButton_CheckedChanged);
            this.rbNode.Click += new System.EventHandler(this.RadioButton_Click);
            // 
            // rbLine
            // 
            this.rbLine.AutoSize = true;
            this.rbLine.Font = new System.Drawing.Font("Microsoft Sans Serif", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(136)));
            this.rbLine.Location = new System.Drawing.Point(6, 17);
            this.rbLine.Name = "rbLine";
            this.rbLine.Size = new System.Drawing.Size(45, 17);
            this.rbLine.TabIndex = 0;
            this.rbLine.TabStop = true;
            this.rbLine.Text = "Line";
            this.rbLine.UseVisualStyleBackColor = true;
            this.rbLine.CheckedChanged += new System.EventHandler(this.RadioButton_CheckedChanged);
            this.rbLine.Click += new System.EventHandler(this.RadioButton_Click);
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.deleteToolStripMenuItem,
            this.toolStripMenuItem2,
            this.removeToolStripMenuItem,
            this.recoveryToolStripMenuItem,
            this.toolStripMenuItem1,
            this.toolStripMenuItem3,
            this.refreshToolStripMenuItem});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.Size = new System.Drawing.Size(139, 126);
            // 
            // deleteToolStripMenuItem
            // 
            this.deleteToolStripMenuItem.Name = "deleteToolStripMenuItem";
            this.deleteToolStripMenuItem.Size = new System.Drawing.Size(138, 22);
            this.deleteToolStripMenuItem.Text = "Delete";
            this.deleteToolStripMenuItem.Click += new System.EventHandler(this.deleteToolStripMenuItem_Click);
            // 
            // toolStripMenuItem2
            // 
            this.toolStripMenuItem2.Name = "toolStripMenuItem2";
            this.toolStripMenuItem2.Size = new System.Drawing.Size(135, 6);
            // 
            // removeToolStripMenuItem
            // 
            this.removeToolStripMenuItem.Name = "removeToolStripMenuItem";
            this.removeToolStripMenuItem.Size = new System.Drawing.Size(138, 22);
            this.removeToolStripMenuItem.Text = "Remove ...";
            this.removeToolStripMenuItem.ToolTipText = "Move in Job To Trash can";
            this.removeToolStripMenuItem.Click += new System.EventHandler(this.removeToolStripMenuItem_Click);
            // 
            // recoveryToolStripMenuItem
            // 
            this.recoveryToolStripMenuItem.Name = "recoveryToolStripMenuItem";
            this.recoveryToolStripMenuItem.Size = new System.Drawing.Size(138, 22);
            this.recoveryToolStripMenuItem.Text = "Recovery ...";
            this.recoveryToolStripMenuItem.ToolTipText = "Move out Job from Trash can";
            this.recoveryToolStripMenuItem.Click += new System.EventHandler(this.recoveryToolStripMenuItem_Click);
            // 
            // toolStripMenuItem1
            // 
            this.toolStripMenuItem1.Name = "toolStripMenuItem1";
            this.toolStripMenuItem1.Size = new System.Drawing.Size(138, 22);
            this.toolStripMenuItem1.Text = "Edit";
            this.toolStripMenuItem1.Click += new System.EventHandler(this.toolStripMenuItem1_Click);
            // 
            // toolStripMenuItem3
            // 
            this.toolStripMenuItem3.Name = "toolStripMenuItem3";
            this.toolStripMenuItem3.Size = new System.Drawing.Size(135, 6);
            // 
            // refreshToolStripMenuItem
            // 
            this.refreshToolStripMenuItem.Name = "refreshToolStripMenuItem";
            this.refreshToolStripMenuItem.Size = new System.Drawing.Size(138, 22);
            this.refreshToolStripMenuItem.Text = "Refresh";
            this.refreshToolStripMenuItem.Click += new System.EventHandler(this.refreshToolStripMenuItem_Click);
            // 
            // contextMenuStrip2
            // 
            this.contextMenuStrip2.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.editToolStripMenuItem});
            this.contextMenuStrip2.Name = "contextMenuStrip2";
            this.contextMenuStrip2.Size = new System.Drawing.Size(106, 26);
            // 
            // editToolStripMenuItem
            // 
            this.editToolStripMenuItem.Name = "editToolStripMenuItem";
            this.editToolStripMenuItem.Size = new System.Drawing.Size(105, 22);
            this.editToolStripMenuItem.Text = "Edit...";
            this.editToolStripMenuItem.Click += new System.EventHandler(this.editToolStripMenuItem_Click);
            // 
            // rbBridgeCST
            //
            this.rbBridgeCST.AutoSize = true;
            this.rbBridgeCST.Location = new System.Drawing.Point(6, 83);
            this.rbBridgeCST.Name = "rbBridgeCST";
            this.rbBridgeCST.Size = new System.Drawing.Size(79, 16);
            this.rbBridgeCST.TabIndex = 22;
            this.rbBridgeCST.TabStop = true;
            this.rbBridgeCST.Text = "Bridge CST";
            this.rbBridgeCST.UseVisualStyleBackColor = true;
            this.rbBridgeCST.CheckedChanged += new System.EventHandler(this.RadioButton_CheckedChanged);
            this.rbBridgeCST.Click += new System.EventHandler(this.RadioButton_Click);
            // 
            // FrmKeepData
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(189)))), ((int)(((byte)(216)))), ((int)(((byte)(243)))));
            this.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Stretch;
            this.ClientSize = new System.Drawing.Size(1024, 654);
            this.Controls.Add(this.pnlMain);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.None;
            this.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.Name = "FrmKeepData";
            this.Text = "KeepData";
            this.Load += new System.EventHandler(this.FrmKeepData_Load);
            this.panel7.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this._dgvDetailGrid)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this._dgvDataGrid)).EndInit();
            this.pnlMain.ResumeLayout(false);
            this.panel9.ResumeLayout(false);
            this.panel8.ResumeLayout(false);
            this.gbKeepData.ResumeLayout(false);
            this.gbKeepData.PerformLayout();
            this.gbKey.ResumeLayout(false);
            this.gbKey.PerformLayout();
            this.gbManualRemoveJobViaCstSeq.ResumeLayout(false);
            this.gbManualRemoveJobViaCstSeq.PerformLayout();
            this.contextMenuStrip1.ResumeLayout(false);
            this.contextMenuStrip2.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Panel panel7;
        private System.Windows.Forms.Panel panel4;
        private System.Windows.Forms.Panel panel3;
        private System.Windows.Forms.Panel panel2;
        private System.Windows.Forms.Panel pnlMain;
        private System.Windows.Forms.Panel panel9;
        private System.Windows.Forms.Panel panel8;
        private System.Windows.Forms.DataGridView _dgvDataGrid;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
        private System.Windows.Forms.ToolStripMenuItem deleteToolStripMenuItem;
        private System.Windows.Forms.ToolStripSeparator toolStripMenuItem2;
        private System.Windows.Forms.ToolStripMenuItem removeToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem recoveryToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem refreshToolStripMenuItem;
        private System.Windows.Forms.ToolStripSeparator toolStripMenuItem3;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem1;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip2;
        private System.Windows.Forms.ToolStripMenuItem editToolStripMenuItem;
        private System.Windows.Forms.GroupBox gbKeepData;
        private System.Windows.Forms.RadioButton rbAlarm;
        private System.Windows.Forms.RadioButton rbWorkTrash;
        private System.Windows.Forms.RadioButton rbWorkWIP;
        private System.Windows.Forms.RadioButton rbNGCrtNo;
        private System.Windows.Forms.RadioButton rbGroupNo;
        private System.Windows.Forms.RadioButton rbCstData;
        private System.Windows.Forms.RadioButton rbPortCst;
        private System.Windows.Forms.RadioButton rbPort;
        private System.Windows.Forms.RadioButton rbNode;
        private System.Windows.Forms.RadioButton rbLine;
        private System.Windows.Forms.Button cmdRefresh;
        private System.Windows.Forms.GroupBox gbKey;
        private System.Windows.Forms.TextBox txtKey;
        private System.Windows.Forms.Label lblKey;
        private System.Windows.Forms.RadioButton rbMaterial;
        private System.Windows.Forms.DataGridView _dgvDetailGrid;
        private System.Windows.Forms.GroupBox gbManualRemoveJobViaCstSeq;
        private System.Windows.Forms.Button btnManualRemoveJobViaCstSeq;
        private System.Windows.Forms.TextBox txtCstSeq;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.RadioButton rbBatchSampling;
        private System.Windows.Forms.RadioButton rbRecipeCommands;
        private System.Windows.Forms.Label lblMemo;
        private System.Windows.Forms.RadioButton rbRTCInfo;
        private System.Windows.Forms.RadioButton rbRTCInspFuncInfo;
        private System.Windows.Forms.RadioButton rbHSInfo;
        private System.Windows.Forms.RadioButton rbUsingGroup;
        private System.Windows.Forms.RadioButton rbRTCParameterInfo;
        private System.Windows.Forms.RadioButton rbShopChangeInfo;
        private System.Windows.Forms.RadioButton rbBridgeCST;



    }
}