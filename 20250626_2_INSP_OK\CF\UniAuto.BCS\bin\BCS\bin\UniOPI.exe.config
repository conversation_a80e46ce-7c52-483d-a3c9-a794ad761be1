﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <section name="nlog" type="NLog.Config.ConfigSectionHandler, NLog" />
  </configSections>
  <connectionStrings>
    <add name="UniOPI.Properties.Settings.UNIBCSConnectionString" connectionString="Data Source=118.163.246.35;Initial Catalog=UNIBCS;User ID=sa;Password=************" providerName="System.Data.SqlClient" />
    <add name="UniOPI.Properties.Settings.UNIBCSConnectionString1" connectionString="Data Source=REBECCA-VAIO\SQLEXPRESS;Initial Catalog=UNIBCS;Integrated Security=True" providerName="System.Data.SqlClient" />
    <add name="UniOPI.Properties.Settings.UNIBCSConnectionString2" connectionString="Data Source=************,1434;Initial Catalog=UNIBCS;Persist Security Info=True;User ID=sa;Password=**************" providerName="System.Data.SqlClient" />
    <add name="UniOPI.Properties.Settings.UNIBCSConnectionString3" connectionString="Data Source=REBECCA-VAIO\SQLEXPRESS;Initial Catalog=UNIBCS;Integrated Security=True" providerName="System.Data.SqlClient" />
    <add name="UniOPI.Properties.Settings.UNIBCSConnectionString4" connectionString="Data Source=************,1434;Initial Catalog=UNIBCS;Persist Security Info=True;User ID=sa" providerName="System.Data.SqlClient" />
    <add name="UniOPI.Properties.Settings.UNIBCS_t3ConnectionString" connectionString="Data Source=************,1442;Initial Catalog=UNIBCS_t3;Persist Security Info=True;User ID=sa;Password=**************" providerName="System.Data.SqlClient" />
    <add name="UniOPI.Properties.Settings.UNIBCSConnectionString5" connectionString="Data Source=************,1442;Initial Catalog=UNIBCS_t3;Persist Security Info=True;User ID=sa" providerName="System.Data.SqlClient" />
    <add name="UniOPI.Properties.Settings.UNIBCS_t3ConnectionString1" connectionString="Data Source=REX-PC;Initial Catalog=UNIBCS_t3;Persist Security Info=True;User ID=sa" providerName="System.Data.SqlClient" />
    <add name="UniOPI.Properties.Settings.UNIBCS_t3ConnectionString2" connectionString="Data Source=REBECCA-PC\SQLEXPRESS2012;Initial Catalog=UNIBCS_t3;Persist Security Info=True;User ID=sa;Password=**************" providerName="System.Data.SqlClient" />
    <add name="UniOPI.Properties.Settings.UNIBCSConnectionString6" connectionString="Data Source=************,1442;Initial Catalog=UNIBCS_t3;User ID=sa;Password=**************" providerName="System.Data.SqlClient" />
    <add name="UniOPI.Properties.Settings.UNIBCS_t3ConnectionString3" connectionString="Data Source=localhost;Initial Catalog=UNIBCS_t3;Persist Security Info=True;User ID=sa" providerName="System.Data.SqlClient" />
    <add name="UniOPI.Properties.Settings.UNIBCS_t3ConnectionString4" connectionString="Data Source=REBECCA-PC\SQLEXPRESS2012;Initial Catalog=UNIBCS_t3;User ID=sa;Password=**************" providerName="System.Data.SqlClient" />
    <add name="UniOPI.Properties.Settings.UNIBCS_t3ConnectionString5" connectionString="Data Source=REBECCA-PC\SQLEXPRESS2012;Initial Catalog=UNIBCS_t3;Persist Security Info=True;User ID=sa" providerName="System.Data.SqlClient" />
    <add name="UniOPI.Properties.Settings.UNIBCS_t3ConnectionString6" connectionString="Data Source=************,1442;Initial Catalog=UNIBCS_t3;Persist Security Info=True;User ID=sa" providerName="System.Data.SqlClient" />
    <add name="UniOPI.Properties.Settings.UNIBCS_INX_CFConnectionString" connectionString="Data Source=127.0.0.1;Initial Catalog=UNIBCS_INX_CF;Persist Security Info=True;User ID=sa" providerName="System.Data.SqlClient" />
    <add name="UniOPI.Properties.Settings.UNIBCS_INX_CFConnectionString1" connectionString="Data Source=118.163.246.35,1433;Initial Catalog=UNIBCS_INX_CF;Persist Security Info=True;User ID=sa;Password=**************" providerName="System.Data.SqlClient" />
    <add name="UniOPI.Properties.Settings.UNIBCS_INX_CFConnectionString2" connectionString="Data Source=192.168.12.175;Initial Catalog=UNIBCS_INX_CF;User ID=sa;Password=**************" providerName="System.Data.SqlClient" />
    <add name="UniOPI.Properties.Settings.UNIBCS_INX_CFConnectionString3" connectionString="Data Source=192.168.12.175,1433;Initial Catalog=UNIBCS_INX_CF;Persist Security Info=True;User ID=sa;Password=**************" providerName="System.Data.SqlClient" />
    <add name="UniOPI.Properties.Settings.UNIBCS_INX_CFConnectionString4" connectionString="Data Source=REBECCA-VAIO\SQLEXPRESS;Initial Catalog=UNIBCS_INX_CF;User ID=sa;Password=**************" providerName="System.Data.SqlClient" />
    <add name="UniOPI.Properties.Settings.UNIBCS_INX_CF_HISTConnectionString" connectionString="Data Source=REBECCA-VAIO\SQLEXPRESS;Initial Catalog=UNIBCS_INX_CF_HIST;User ID=sa;Password=**************" providerName="System.Data.SqlClient" />
    <add name="UniOPI.Properties.Settings.UNIBCS_INX_CFConnectionString5" connectionString="Data Source=192.168.12.175,1433;Initial Catalog=UNIBCS_INX_CF;User ID=sa;Password=**************" providerName="System.Data.SqlClient" />
    <add name="UniOPI.Properties.Settings.UNIBCS_INX_CF_HISTConnectionString1" connectionString="Data Source=192.168.12.175,1433;Initial Catalog=UNIBCS_INX_CF_HIST;User ID=sa;Password=**************" providerName="System.Data.SqlClient" />
    <add name="UniOPI.Properties.Settings.UNIBCS_INX_CFConnectionString6" connectionString="Data Source=REBECCA-VAIO\SQLEXPRESS;Initial Catalog=UNIBCS_INX_CF;Persist Security Info=True;User ID=sa;Password=**************" providerName="System.Data.SqlClient" />
    <add name="UniOPI.Properties.Settings.UNIBCS_INX_CFConnectionString7" connectionString="Data Source=REBECCA-PC\SQLEXPRESS2012;Initial Catalog=UNIBCS_INX_CF;Persist Security Info=True;User ID=sa;Password=**************" providerName="System.Data.SqlClient" />
    <add name="UniOPI.Properties.Settings.UNIBCS_INX_CFConnectionString8" connectionString="Data Source=192.168.12.175,1433;Initial Catalog=UNIBCS_INX_CF;Persist Security Info=True;User ID=sa;Password=**************" providerName="System.Data.SqlClient" />
    <add name="UniOPI.Properties.Settings.UNIBCS_INX_CF_DATAConnectionString1" connectionString="Data Source=192.168.12.175;Initial Catalog=UNIBCS_INX_CF_DATA;Persist Security Info=True;User ID=sa" providerName="System.Data.SqlClient" />
    <add name="UniOPI.Properties.Settings.UNIBCS_INX_CF_HISTConnectionString2" connectionString="Data Source=192.168.12.175;Initial Catalog=UNIBCS_INX_CF_HIST;Persist Security Info=True;User ID=sa" providerName="System.Data.SqlClient" />
    <add name="UniOPI.Properties.Settings.UNIBCS_INX_CF_HISTConnectionString3" connectionString="Data Source=192.168.12.175,1433;Initial Catalog=UNIBCS_INX_CF_HIST;Persist Security Info=True;User ID=sa;Password=**************" providerName="System.Data.SqlClient" />
    <add name="UniOPI.Properties.Settings.UNIBCS_INX_CF_DATAConnectionString" connectionString="Data Source=REBECCA-PC\SQLEXPRESS2012;Initial Catalog=UNIBCS_INX_CF_DATA;Persist Security Info=True;User ID=sa;Password=**************" providerName="System.Data.SqlClient" />
    <add name="UniOPI.Properties.Settings.UNIBCS_INX_CF_DATAConnectionString2" connectionString="Data Source=REBECCA-PC\SQLEXPRESS2012;Initial Catalog=UNIBCS_INX_CF_DATA;Persist Security Info=True;User ID=sa;Password=**************" providerName="System.Data.SqlClient" />
    <add name="UniOPI.Properties.Settings.UNIBCS_INX_CF_HISTConnectionString4" connectionString="Data Source=192.168.12.175;Initial Catalog=UNIBCS_INX_CF_HIST;Persist Security Info=True;User ID=sa;Password=**************" providerName="System.Data.SqlClient" />
    <add name="UniOPI.Properties.Settings.UNIBCS_INX_CFConnectionString10" connectionString="Data Source=REBECCA-PC\SQLEXPRESS2012;Initial Catalog=UNIBCS_INX_CF;Persist Security Info=True;User ID=sa;Password=**************" providerName="System.Data.SqlClient" />
    <add name="UniOPI.Properties.Settings.UNIBCS_INX_CF_DATAConnectionString3" connectionString="Data Source=192.168.12.175;Initial Catalog=UNIBCS_INX_CF_DATA;Persist Security Info=True;User ID=sa;Password=**************" providerName="System.Data.SqlClient" />
    <add name="UniOPI.Properties.Settings.UNIBCS_INX_CFConnectionString9" connectionString="Data Source=REBECCA-PC\SQLEXPRESS2012;Initial Catalog=UNIBCS_INX_CF;Persist Security Info=True;User ID=sa" providerName="System.Data.SqlClient" />
    <add name="UniOPI.Properties.Settings.UNIBCS_INX_CF_HISTConnectionString5" connectionString="Data Source=192.168.12.175,1433;Initial Catalog=UNIBCS_INX_CF_HIST;Persist Security Info=True;User ID=sa" providerName="System.Data.SqlClient" />
    <add name="UniOPI.Properties.Settings.UNIBCS_INX_CFConnectionString11" connectionString="Data Source=192.168.12.175;Initial Catalog=UNIBCS_INX_CF;Persist Security Info=True;User ID=sa" providerName="System.Data.SqlClient" />
    <add name="UniOPI.Properties.Settings.UNIBCS_INX_CFConnectionString12" connectionString="Data Source=C177\SQLEXPRESS;Initial Catalog=UNIBCS_INX_CF;User ID=sa" providerName="System.Data.SqlClient" />
  </connectionStrings>
  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.0" />
  </startup>
  <nlog autoReload="true" throwExceptions="true">
    <targets>
      <target name="TraceLog" type="File" deleteOldFileOnStartup="false" layout="${longdate}[${pad:padding=-5:inner=${level:uppercase=true}}]${message}" fileName="D:/UnicomLog/INSP0100/BCS/OPI/${date:format=yyyyMMdd}/Trace/OPI_Trace_${date:format=HH}.log" archiveFileName="D:/UnicomLog/INSP0100/BCS/OPI/${date:format=yyyyMMdd}/Trace/OPI_Trace_${date:format=HH}.{#####}.log" archiveAboveSize="20971520" archiveNumbering="Sequence" MaxArchiveFiles="50" concurrentWrites="true" keepFileOpen="false" />
      <target name="ErrorLog" type="File" deleteOldFileOnStartup="false" layout="${longdate}[${pad:padding=-5:inner=${level:uppercase=true}}]${message}" fileName="D:/UnicomLog/INSP0100/BCS/OPI/${date:format=yyyyMMdd}/Error/OPI_Error_${date:format=HH}.log" archiveFileName="D:/UnicomLog/INSP0100/BCS/OPI/${date:format=yyyyMMdd}/Error/OPI_Error_${date:format=HH}.{#####}.log" archiveAboveSize="20971520" archiveNumbering="Sequence" MaxArchiveFiles="50" concurrentWrites="true" keepFileOpen="false" />
      <target name="InfoLog" type="File" deleteOldFileOnStartup="false" layout="${longdate}  ${message}" fileName="D:/UnicomLog/INSP0100/BCS/OPI/${date:format=yyyyMMdd}/Info/OPI_Info_${date:format=HH}.log" archiveFileName="D:/UnicomLog/INSP0100/BCS/OPI/${date:format=yyyyMMdd}/Info/OPI_Info_${date:format=HH}.{#####}.log" archiveAboveSize="20971520" archiveNumbering="Sequence" MaxArchiveFiles="50" concurrentWrites="true" keepFileOpen="false" />
    </targets>
    <!--ClientTraceLog的全域變數，決定要記錄到哪個層級-->
    <variable name="TraceGlobalLevelLog" value="(level == LogLevel.Trace)" />
    <variable name="TraceGlobalLevelIgnore" value="(level == LogLevel.Debug) or (level == LogLevel.Info) or (level == LogLevel.Warn) or (level == LogLevel.Error) or (level == LogLevel.Fatal)  " />
    <variable name="InfoGlobalLevelLog" value="(level == LogLevel.Info)" />
    <variable name="InfoGlobalLevelIgnore" value="(level == LogLevel.Debug) or (level == LogLevel.Trace) or (level == LogLevel.Warn) or (level == LogLevel.Error) or (level == LogLevel.Fatal)  " />
    <rules>
      <!--Trace Log -->
      <logger name="*" minlevel="Trace" writeTo="">
        <!-- minlevel : Trace->Debug->Info->Warn->Error->Fatal -->
        <filters>
          <when condition="${TraceGlobalLevelLog}" action="Log" />
          <when condition="${TraceGlobalLevelIgnore}" action="Ignore" />
        </filters>
      </logger>
      <!--Error Log -->
      <logger name="*" levels="Warn,Error" writeTo="ErrorLog">
      </logger>
      <!--Info Log -->
      <logger name="*" levels="Info" writeTo="">
      </logger>
    </rules>
  </nlog>
  <!-- 
  layout="發生時間：${longdate}${newline}
        類別：${logger}${newline}
        訊息：${message}${newline}
        方法：${stacktrace:format=DetailedFlat}${newline}
        例外堆疊：${exception:format=ToString}${newline}
  -->
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <probing privatePath=".;dll" />
    </assemblyBinding>
  </runtime>
  <appSettings>
    <add key="FabType" value="CF" />
    <add key="LineType" value="INSP0100" />
    <add key="LineID" value="INSP0100" />
    <add key="AutoLogoutTime" value="0" />
    <add key="LayoutFolder" value="..\Config\OPI\Layout\" />
    <add key="ParamFolder" value="..\Config\OPI\Param\DBConfig_TEST.xml" />
    <add key="TimingChartFolder" value="..\Config\OPI\TimingChart\" />
    <add key="OPIMaxCount" value="25" />
    <add key="HistoryOnly" value="N" />
  </appSettings>
</configuration>